import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { Container } from 'pixi.js';
import { isNil, sample } from 'lodash-unified';
import { Logger, Orientation, t } from '@bg-shared';
import { LuckyLennySound, luckyLennySoundsService } from '../../../../business/services/Sounds';
import {
    ILuckyLennyBonusGameboardClickEvent,
    ILuckyLennyEventManager,
    ILuckyLennyResultEvent,
    ILuckyLennySwitchGameEvent,
    ILuckyLennyWinEvent,
    IPlayResponse,
    IRoundResult,
} from '../../../../interfaces';
import type { IBoardGameMultipliers } from '../../../objects/bonusBoardGame/interfaces';
import { ExplosionTile } from '../../../objects/bonusBoardGame/ExplosionTile';
import { BoardGrid } from '../../../objects/bonusBoardGame/BoardGrid';
import { LennyBoard } from '../../../objects/bonusBoardGame/LennyBoard';
import { LuckyLennyEvents, LuckyLennyGameId, NextAction } from '../../../../enums';
import { BonusGame } from '../../../objects/BonusGame';
import { BonusGameboardInfoMessage } from '../../../objects/BonusGameboardInfoMessage';
import type { IGameManager } from '../../../interfaces/gameManager';
import { GameManagerBase } from '../GameManagerBase';
import { mapBoardSymbolIndexToMultiplier } from '../../../utils/bonusBoardMappings';
import { LennyController } from '../../LennyController';
import { LAYER_ORDER } from '../../../config';
import { gameConfigSelectors } from '../../../../business/stores/gameConfig.selectors';
import { ParticleSystem } from '../../../objects/particle';
import particlesConfig from '../../../config/particlesConfig';

export class BonusBoardGameController extends GameManagerBase implements IGameManager {
    private readonly game: LennyBoard;

    private readonly showResultTimeoutMs = 2000;

    private readonly oneByOneExplosionDelayMs = 250;

    private multiplier: number;

    private readonly oneByOneExplosionTimerIds: number[] = [];

    private closed = false;

    private showResultTimeoutId: number;

    private autoClickAvailableIndexes: number[] = [];

    private readonly showGlowItemCountThreshold = 3;

    // Indicates whether the controller is running in automatic play mode (user clicks disabled)
    private autoPlayMode = false;

    // Indicates whether user interaction is enabled
    private userInteractionEnabled = true;

    private readyForNextClick = true;

    private readonly multiplierCounts = new Map<number, number>();

    private grid: BoardGrid;

    private tiles: ExplosionTile<IBoardGameMultipliers>[];

    private preOpenIndexes: number[] = [];

    // Store unsubscribe functions for event listeners
    private eventUnsubscribers: (() => void)[] = [];

    private particleSystem: ParticleSystem;

    private infoMessage: BonusGameboardInfoMessage;

    constructor(
        stage: Container,
        viewport: PixiViewport,
        eventManager: ILuckyLennyEventManager,
        initialValues?: number[],
    ) {
        super(stage, viewport, eventManager);

        this.game = new LennyBoard();

        this.bonusGame = new BonusGame(this.game, this.stage, this.viewport);

        this.activeGameContainer.addChild(this.bonusGame.view);

        this.activeGameContainer.zIndex = LAYER_ORDER.MAIN_GAME;

        this.infoMessage = new BonusGameboardInfoMessage();
        this.activeGameContainer.addChild(this.infoMessage.view);

        this.viewport.layoutManager.register(this.activeGameContainer);
        this.viewport.layoutManager.update();

        this.particleSystem = new ParticleSystem({
            ...particlesConfig,
            enabled: true,
            ticker: this.viewport.ticker,
        });
        this.activeGameContainer.addChild(this.particleSystem.container);

        // Set initial bounds for particle system
        this.particleSystem.updateBounds(this.viewport);

        // Initialize the particle system after it's added to the scene
        this.particleSystem.initialize();

        this.eventUnsubscribers.push(
            this.eventManager.on<ILuckyLennyResultEvent>(
                LuckyLennyEvents.bonusGameboardResult,
                () => {
                    this.eventManager.trigger<ILuckyLennyWinEvent>(LuckyLennyEvents.winAmount, {
                        winAmount: this.winAmount,
                        multiplier: this.multiplier,
                    });
                },
            ),
            this.eventManager.on(LuckyLennyEvents.bonusGameboardAutoPlay, () => {
                this.startAutoPlay();
            }),
            this.eventManager.on(LuckyLennyEvents.bonusGameboardClick, () => {
                this.infoMessage.updateText(t.string('matching_multipliers_result_in_a_price'));
                this.infoMessage.disableGlow();
            }),
        );

        this.setup(initialValues as IBoardGameMultipliers[]);
        this.startGame();
        this.eventUnsubscribers.push(
            this.eventManager.on(LuckyLennyEvents.winAnimationEnd, this.winAnimationEnd),
        );
    }

    public registerTopLevelContainer(topLevelContainer: Container) {
        this.viewport.layoutManager.register(topLevelContainer);
    }

    public setup(values?: IBoardGameMultipliers[]): void {
        this.grid = new BoardGrid();
        this.autoClickAvailableIndexes.length = 0;
        this.preOpenIndexes.length = 0;
        this.multiplierCounts.clear();

        const gridLength = this.grid.length;

        if (!values) {
            // Initialize all tiles as unopened (-1) until backend provides actual values.
            for (let i = 0; i < gridLength; i++) {
                this.grid.setValueByIndex(-1, i);
            }

            this.infoMessage.updateText(t.string('tap_titles_to_find_matching_multipliers'));
            this.infoMessage.enableGlow();
        } else {
            values.forEach((value, index) => {
                this.grid.setValueByIndex(value, index);

                // Track pre-opened tiles
                if (value !== -1) {
                    this.preOpenIndexes.push(index);
                    this.incrementMultiplierCount(value);
                }
            });

            this.infoMessage.updateText(t.string('matching_multipliers_result_in_a_price'));
            this.infoMessage.disableGlow();
        }

        // Add clickable indexes for unopened tiles
        for (let i = 0; i < gridLength; i++) {
            const value = this.grid.getValue(i);
            if (value === -1) {
                this.autoClickAvailableIndexes.push(i);
            }
        }

        this.tiles = this.game.addTiles(this.grid, this.onBoardClick);
    }

    public startGame() {
        this.game.start();
        this.tiles.forEach((tile) => {
            tile.visible = true;

            // Disable user interactions if user interaction is not enabled OR if tile has initial value
            if (!this.userInteractionEnabled || this.grid.getValue(tile.index) !== -1) {
                tile.removeClickHandler();
            }
        });

        // Open tiles predefined by BE after the board becomes visible
        this.preOpenIndexes.forEach((index) => {
            const tile = this.tiles[index];
            const value = this.grid.getValue(index);
            tile.setValue(value);
            tile.forceExplosion();
            this.incrementMultiplierCount(value);
        });

        this.enableUserInteraction();
    }

    public dispose() {
        // Unsubscribe from all event listeners
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        clearTimeout(this.showResultTimeoutId);

        this.oneByOneExplosionTimerIds.forEach((timerId) => {
            clearTimeout(timerId);
        });
        this.oneByOneExplosionTimerIds.length = 0;

        this.tiles.forEach((tile) => tile.destroy());
        this.tiles.length = 0;

        this.game?.destroy();
        this.particleSystem?.destroy();

        super.dispose();
    }

    private onBoardClick = (tile: ExplosionTile<IBoardGameMultipliers>): void => {
        if (!this.readyForNextClick) {
            return;
        }

        if (isNil(tile.index)) {
            Logger.error(
                'BonusBoardGameController: Tile index is null or undefined',
                'lenny-bonusBoardGame',
            );
            return;
        }

        this.eventManager.trigger<ILuckyLennyBonusGameboardClickEvent>(
            LuckyLennyEvents.bonusGameboardClick,
            tile.index,
        );
        tile.setPending(true);
        this.readyForNextClick = false;
    };

    public onBonusGameboardClickResponse(response: IPlayResponse, symbolIndex: number): void {
        super.onBonusGameboardClickResponse(response, symbolIndex);

        const { lastRoundResult } = response;

        this.winAmount = gameConfigSelectors.getLastGameStateWinAmount(response);
        this.totalWinAmount = gameConfigSelectors.getTotalAmountWon(response);

        const roundRes = lastRoundResult as IRoundResult;
        this.multiplier = roundRes.totalMultiplier;
        this.closed = Boolean(roundRes?.closed);
        const setIndex = roundRes?.roundVariables?.setIndex ?? 0;

        if (this.closed && Array.isArray(roundRes?.symbols)) {
            roundRes.symbols.forEach((symbolValue, idx) => {
                if (idx === symbolIndex) {
                    return;
                }

                const mappedValue = mapBoardSymbolIndexToMultiplier(
                    symbolValue,
                    setIndex,
                ) as number;

                if (this.grid.getValue(idx) === -1) {
                    this.grid.setValueByIndex(mappedValue as IBoardGameMultipliers, idx);

                    const tileToUpdate = this.tiles[idx];
                    if (tileToUpdate) {
                        tileToUpdate.setValue(mappedValue as IBoardGameMultipliers);
                    }

                    this.incrementMultiplierCount(mappedValue);
                }
            });
        } else if (!this.autoPlayMode) {
            this.enableUserInteraction();
        }

        const rawValue = roundRes?.symbols?.[symbolIndex] ?? -1;
        const value = mapBoardSymbolIndexToMultiplier(rawValue, setIndex) as number;

        this.openTile(symbolIndex, value as IBoardGameMultipliers);
    }

    public activeLenny(lennyController: LennyController): void {
        super.activeLenny(lennyController);

        if (!this.game?.board?.view) {
            Logger.error(
                'BonusBoardGameController: activeLenny - game.board.view is null, cannot add lenny to game',
                'lenny-bonusBoardGame',
            );
            return;
        }

        this.lennyController.addLennyToContainer(this.activeGameContainer);
        if (this.isMobileView) {
            this.lennyController.moveTo(-350, 550);
            this.lennyController.scaleDownForBonusGame(0.66);
        } else {
            this.lennyController.moveTo(-520, 150);
            this.lennyController.scaleDownForBonusGame(1);
        }
    }

    private openTile(index: number, value: IBoardGameMultipliers) {
        const tile = this.tiles[index];

        if (!tile) {
            console.error(
                `BonusBoardGameController: Tile at index ${index} not found. Tiles array length: ${this.tiles.length}`,
            );
            return;
        }

        tile.setPending(false);
        tile.setValue(value);
        tile.forceExplosion();

        this.grid.setValueByIndex(value, index);
        this.incrementMultiplierCount(value);

        if (!this.isWinCondition() && this.grid.getTheSameValueIndexes(tile.index).length > 1) {
            luckyLennySoundsService.play(LuckyLennySound.FindingMatch);
        }

        if (this.getMultiplierCount(value) >= this.showGlowItemCountThreshold) {
            const indexes = this.grid.getTheSameValueIndexes(tile.index);

            indexes.forEach((i) => {
                const expectedTile = this.tiles[i];

                if (expectedTile && expectedTile.opened && !expectedTile.glowed) {
                    expectedTile.glow();
                }
            });

            tile.glow();
        }

        // Remove from list of still-clickable tiles
        const autoClickIndexToRemove = this.autoClickAvailableIndexes.findIndex(
            (val) => val === tile.index,
        );

        if (autoClickIndexToRemove !== -1) {
            this.autoClickAvailableIndexes.splice(autoClickIndexToRemove, 1);
        }

        this.readyForNextClick = true;

        if (this.isWinCondition()) {
            luckyLennySoundsService.play(LuckyLennySound.RegularPrizeWon);
            this.showWin();
        } else if (this.autoPlayMode && !this.closed) {
            // Continue automatic play until the round ends
            this.autoClickRandomTile();
        }
    }

    private isWinCondition = () => {
        return this.closed;
    };

    // Particle system control methods
    public enableParticles(): void {
        if (this.particleSystem) {
            this.particleSystem.setEnabled(true);
        }
    }

    public disableParticles(): void {
        if (this.particleSystem) {
            this.particleSystem.setEnabled(false);
        }
    }

    public setParticleIntensity(intensity: number): void {
        if (this.particleSystem) {
            // Adjust spawn rate based on intensity (0-1)
            const spawnRate = Math.max(0.01, Math.min(0.2, intensity * 0.2));
            this.particleSystem.setSpawnRate(spawnRate);

            // Adjust max particles based on intensity
            const maxParticles = Math.floor(intensity * 50) + 10;
            this.particleSystem.setMaxParticles(maxParticles);
        }
    }

    private showWin = () => {
        this.autoClickAvailableIndexes.forEach((index, i) => {
            const item = this.tiles[index];

            item.removeClickHandler();

            this.oneByOneExplosionTimerIds.push(
                setTimeout(() => {
                    item.forceExplosion();
                }, i * this.oneByOneExplosionDelayMs),
            );
        });

        this.showResultTimeoutId = setTimeout(
            () => {
                this.eventManager.trigger(LuckyLennyEvents.bonusGameboardResult);
            },
            this.showResultTimeoutMs +
                (this.autoClickAvailableIndexes.length - 1) * this.oneByOneExplosionDelayMs,
        );

        this.infoMessage.hide();
    };

    private winAnimationEnd = (): void => {
        const isStandard = this.nextAction === NextAction.STANDARD;

        this.eventManager.trigger<ILuckyLennySwitchGameEvent>(LuckyLennyEvents.switchGame, {
            gameId: isStandard ? LuckyLennyGameId.LennySlotGame : LuckyLennyGameId.LennyWheelGame,
            ...(isStandard ? { totalWinAmount: this.totalWinAmount } : {}),
        });
    };

    private incrementMultiplierCount = (value: number): void => {
        const currentCount = this.multiplierCounts.get(value) || 0;
        this.multiplierCounts.set(value, currentCount + 1);
    };

    private getMultiplierCount = (value: number): number => this.multiplierCounts.get(value) || 0;

    /**
     * Enables automatic play mode – user clicks are disabled and the system will keep
     * selecting random tiles until the round ends.
     */
    private startAutoPlay = () => {
        this.disableUserInteraction();

        this.autoPlayMode = true;

        // Kick-off automatic play with the first random tile
        this.autoClickRandomTile();
    };

    /**
     * Enables user interaction mode - allows users to click tiles manually
     */
    private enableUserInteraction(): void {
        this.userInteractionEnabled = true;
        this.autoPlayMode = false;

        // Re-enable click handlers for unopened tiles
        this.tiles.forEach((tile) => {
            if (!tile.opened) {
                tile.addClickHandler();
            }
        });
    }

    /**
     * Disables user interaction and switches to auto-play mode
     */
    private disableUserInteraction(): void {
        this.userInteractionEnabled = false;

        // Disable any user interactions on tiles
        this.tiles.forEach((tile) => {
            tile.removeClickHandler();
        });
    }

    /**
     * Picks a random unopened tile and simulates a click.
     * Will not trigger if the controller is not ready for the next click or the round is closed.
     */
    private autoClickRandomTile = () => {
        if (this.closed || !this.readyForNextClick || !this.autoClickAvailableIndexes.length) {
            return;
        }

        // Pick a random index from available unopened tiles
        const tileIndex = sample(this.autoClickAvailableIndexes);
        const tile = this.tiles[tileIndex];

        if (tile && !tile.opened) {
            this.onBoardClick(tile);
        }
    };

    // Test method to help debug lenny view issues
    public testLennyView(): void {
        if (this.lennyController) {
            const lenny = this.lennyController.getLenny();
            Logger.log('BonusBoardGameController: lenny exists:', !!lenny);
            Logger.log('BonusBoardGameController: lenny.container exists:', !!lenny?.container);
            Logger.log(
                'BonusBoardGameController: lenny.container.parent exists:',
                !!lenny?.container?.parent,
            );
        }
    }

    /**
     * Handle resize events and update particle system bounds
     */
    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        super.handleResize(orientation, isMobileView);

        // Update particle system bounds to match viewport
        if (this.particleSystem && !this.particleSystem.container.destroyed) {
            this.particleSystem.updateBounds(this.viewport);
        }

        this.infoMessage.handleResize();
    }
}
