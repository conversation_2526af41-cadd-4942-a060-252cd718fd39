import { Container } from 'pixi.js';
import gsap from 'gsap';
import { ICoordinates, Logger, Orientation } from '@bg-shared';
import { LuckyLennySound, luckyLennySoundsService } from '../../business/services/Sounds';
import { LENNY_CELEBRATION_HANDS_UP_TIMES } from '../../constants';
import { LAYER_ORDER } from '../config';
import { LuckyLennyEvents, SymbolType } from '../../enums';
import { Lenny } from '../objects/Lenny';
import type { ILuckyLennyEventManager, ILennySpeedConfig } from '../../interfaces';
import { getLennySpeedConfig } from '../config/lennySpeedConfig';

type IOnCollectCallback = (position: ICoordinates, symbol: SymbolType) => void;

export class LennyController {
    private lenny: Lenny;

    private isFlying = false;

    private onCollectCallbacks: IOnCollectCallback[] = [];

    private speedConfig: ILennySpeedConfig;

    private flightSpeed: number;

    private horizontalSpeedFactor: number;

    private verticalSpeedFactor: number;

    private smoothCollectTween: gsap.core.Tween | null = null;

    private verticalMovementTween: gsap.core.Tween | null = null;

    private verticalMovementRange: number; // How much Lenny moves up/down fake animation

    private verticalMovementDuration: number; // Duration of one up/down cycle

    private currentVerticalOffset = 0; // Current vertical offset for animation

    private parentLennyContainer?: Container;

    constructor(private readonly eventManager: ILuckyLennyEventManager) {
        this.lenny = new Lenny();
        this.lenny.startIdleAnimation();

        // Initialize speed configuration from branded config
        this.initializeSpeedConfig();

        this.x = 0;
        this.y = 0;
    }

    public get x() {
        return this.lenny.x;
    }

    public set x(value: number) {
        this.lenny.x = value;
    }

    public get y() {
        return this.lenny.y;
    }

    public set y(value: number) {
        this.lenny.y = value;
    }

    private initializeSpeedConfig(): void {
        this.speedConfig = getLennySpeedConfig();

        // Initialize speed values from config
        this.flightSpeed = this.speedConfig.speed.basicFlightSpeed;
        this.horizontalSpeedFactor = this.speedConfig.speed.horizontalSpeedFactor;
        this.verticalSpeedFactor = this.speedConfig.speed.verticalSpeedFactor;
        this.verticalMovementRange = this.speedConfig.animation.verticalMovementRange;
        this.verticalMovementDuration = this.speedConfig.animation.verticalMovementDuration;
    }

    public turboModeEnable(enable: boolean) {
        const turboSpeedFactor = this.speedConfig.speed.turboModeSpeedFactor;
        this.flightSpeed = enable
            ? this.speedConfig.speed.basicFlightSpeed / turboSpeedFactor
            : this.speedConfig.speed.basicFlightSpeed;

        this.lenny.setCelebrationCount(
            LENNY_CELEBRATION_HANDS_UP_TIMES * (enable ? turboSpeedFactor : 1),
        );
    }

    public celebration() {
        this.lenny.stopIdleAnimation();
        this.lenny.celebrationAnimation();
    }

    // Move to a specific position
    public moveTo(x: number, y: number): void {
        this.x = x;
        this.y = y;
    }

    public getPosition() {
        return this.lenny.container.position;
    }

    public getLenny(): Lenny {
        return this.lenny;
    }

    public isLennyFlying(): boolean {
        return this.isFlying;
    }

    public reset(): void {
        if (this.isFlying) {
            this.stopFlyingSounds();
        }

        this.isFlying = false;
        this.stopVerticalMovement();
        this.currentVerticalOffset = 0;
        this.lenny.reset();
        this.lenny.startIdleAnimation();
    }

    public startFlyingToCollectByPositions(
        symbolsToCollect: SymbolType[],
        symbolPositions: Array<ICoordinates>,
    ): Promise<void> {
        this.isFlying = true;
        this.lenny.startFlyingAnimation();

        luckyLennySoundsService.playLoop(LuckyLennySound.JetpackStart);

        // Start vertical movement animation when flying
        this.startVerticalMovement();

        return symbolPositions.reduce((deferred, position, i) => {
            return deferred.finally(() => {
                return this.smoothMoveToPosition(position).finally(() => {
                    const symbolType = symbolsToCollect[i];

                    this.onCollectCallbacks.forEach((cb) => cb(position, symbolType));

                    if (symbolType === SymbolType.RESPIN) {
                        this.eventManager.trigger(LuckyLennyEvents.respin);
                    }
                });
            });
        }, Promise.resolve());
    }

    public finishFlying(targetPosition: ICoordinates): Promise<void> {
        return this.smoothMoveToPosition(targetPosition).finally(() => {
            this.isFlying = false;
            this.stopFlyingSounds();
            this.stopVerticalMovement();
            this.lenny.startIdleAnimation();
        });
    }

    public getFlyStepDuration({ x, y }: ICoordinates) {
        const dx = Math.abs(x - this.x);
        const dy = Math.abs(y - this.y);

        return Math.max(
            dx / (this.flightSpeed * this.horizontalSpeedFactor),
            dy / (this.flightSpeed * this.verticalSpeedFactor),
        );
    }

    private smoothMoveToPosition({ x, y }: ICoordinates): Promise<void> {
        const duration = this.getFlyStepDuration({ x, y });

        return new Promise((resolve) => {
            this.smoothCollectTween = gsap.to(this, {
                x,
                y,
                duration,
                ease: 'none',
                onComplete: resolve,
                onInterrupt: () => {
                    if (this.lenny.container.position) {
                        this.x = x;
                        this.y = y;
                        resolve();
                    }
                },
            });
        });
    }

    public addLennyToContainer(container: Container): void {
        container.addChild(this.lenny.container);
        this.lenny.container.zIndex = LAYER_ORDER.CHARACTER;
        this.lenny.startIdleAnimation();
    }

    /**
     * Scale down Lenny to 50% of his normal size for bonus mini-games.
     */
    public scaleDownForBonusGame(scale = 0.8): void {
        this.lenny.scaleDownForBonusGame(scale);
    }

    public release(): void {
        Logger.log('LennyController: release - detaching Lenny from current parent');
        if (this.lenny.container.parent) {
            this.lenny.container.parent.removeChild(this.lenny.container);
            Logger.log('LennyController: release complete');
        } else {
            Logger.log('LennyController: release - no parent to detach from');
        }
    }

    public bringLennyToContainer(topLevelContainer: Container) {
        this.parentLennyContainer = this.lenny.container.parent;
        this.setLennyParentContainer(topLevelContainer);
    }

    public bringLennyBack() {
        this.setLennyParentContainer(this.parentLennyContainer);
    }

    private setLennyParentContainer(newContainer: Container) {
        const globalPoint = this.lenny.container.getGlobalPosition();

        newContainer.addChild(this.lenny.container);

        const localPoint = newContainer.toLocal(globalPoint);

        this.lenny.container.position.copyFrom(localPoint);
    }

    /**
     * Register callback executed each time Lenny collects a symbol.
     */
    public onCollect(callback: IOnCollectCallback): void {
        this.onCollectCallbacks.push(callback);
    }

    /**
     * Start vertical movement animation based on current side
     */
    public startVerticalMovement(): void {
        this.currentVerticalOffset = 0;

        // Create a tween that updates the vertical offset value
        this.verticalMovementTween = gsap.to(this, {
            currentVerticalOffset: this.verticalMovementRange,
            duration: this.verticalMovementDuration * 0.5,
            ease: 'sine.inOut',
            yoyo: true,
            repeat: -1,
        });
    }

    /**
     * Stop vertical movement animation
     */
    private stopVerticalMovement(): void {
        if (this.verticalMovementTween) {
            this.verticalMovementTween.kill();
            this.verticalMovementTween = null;
        }
        this.currentVerticalOffset = 0;
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        this.lenny.handleResize(orientation, isMobileView);
    }

    /**
     * Get the current vertical offset for the animation.
     */
    public getVerticalMovementOffset(): number {
        // eslint-disable-next-line no-bitwise
        return this.currentVerticalOffset >> 0;
    }

    /**
     * Test method to quickly switch between different speed configurations
     * This is useful for testing different speed settings during development
     */
    public testSpeedConfig(isOriginal: boolean): void {
        const config = getLennySpeedConfig(isOriginal);

        if (config) {
            this.updateSpeedConfig(config);
            // eslint-disable-next-line no-console
            console.log(
                `Lenny speed config changed to: ${isOriginal ? 'original A' : 'B'}`,
                config,
            );
        }
    }

    private stopFlyingSounds(): void {
        luckyLennySoundsService.stopLoop(LuckyLennySound.JetpackStart);
        luckyLennySoundsService.play(LuckyLennySound.JetpackStop);
    }

    /**
     * Update speed configuration dynamically
     */
    public updateSpeedConfig(newConfig: ILennySpeedConfig): void {
        this.speedConfig = newConfig;

        // Update speed values from new config
        this.flightSpeed = this.speedConfig.speed.basicFlightSpeed;
        this.horizontalSpeedFactor = this.speedConfig.speed.horizontalSpeedFactor;
        this.verticalSpeedFactor = this.speedConfig.speed.verticalSpeedFactor;
        this.verticalMovementRange = this.speedConfig.animation.verticalMovementRange;
        this.verticalMovementDuration = this.speedConfig.animation.verticalMovementDuration;
    }

    /**
     * Get current speed configuration
     */
    public getSpeedConfig(): ILennySpeedConfig {
        return this.speedConfig;
    }

    /**
     * Get current flight speed
     */
    public getFlightSpeed(): number {
        return this.flightSpeed;
    }

    /**
     * Get current horizontal speed factor
     */
    public getHorizontalSpeedFactor(): number {
        return this.horizontalSpeedFactor;
    }

    /**
     * Get current vertical speed factor
     */
    public getVerticalSpeedFactor(): number {
        return this.verticalSpeedFactor;
    }

    public dispose(): void {
        this.verticalMovementTween?.kill();
        this.verticalMovementTween = null;
        this.smoothCollectTween?.kill();
        this.smoothCollectTween = null;
        this.onCollectCallbacks = [];
        this.lenny.dispose();
    }
}
