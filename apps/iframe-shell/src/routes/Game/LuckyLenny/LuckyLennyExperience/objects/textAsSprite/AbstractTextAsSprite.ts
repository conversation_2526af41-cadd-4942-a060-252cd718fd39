import { Sprite, Texture } from 'pixi.js';
import gsap from 'gsap';
import { FADE_DURATION } from '../../../constants';
import { AbstractContainer } from '../AbstractContainer';

export abstract class AbstractTextAsSprite extends AbstractContainer {
    private readonly sprite: Sprite;

    private readonly fadeDuration: number;

    private tween: gsap.core.Tween | null = null;

    constructor(texture: Texture, fadeDuration = FADE_DURATION) {
        super();

        this.fadeDuration = fadeDuration;
        this.sprite = this.createSprite(texture);

        this.visible = false;
        this.alpha = 0;

        this.addChild(this.sprite);
    }

    public show() {
        this.visible = true;
        this.tween?.kill();
        this.tween = gsap.to(this, {
            alpha: 1,
            duration: this.fadeDuration,
        });
    }

    public hide() {
        this.tween?.kill();
        this.tween = gsap.to(this, {
            alpha: 0,
            duration: this.fadeDuration,
            onComplete: () => this.destroy(),
        });
    }

    public destroy() {
        this.tween?.kill();
        this.tween = null;
        this.sprite.destroy();
        super.destroy();
    }
}
