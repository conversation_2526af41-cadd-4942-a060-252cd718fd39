import { BlurFilter, Container, Sprite, Graphics } from 'pixi.js';
import { Logger, VIDEO_ASPECT_RATIO } from '@bg-shared';
import gsap from 'gsap';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import {
    BASIC_BACKGROUND_SCALE,
    getMainBackgroundLayerConfigs,
} from '../../config/backgroundsConfig';
import type {
    IBackgroundLayerGroup,
    IBackgroundLayerTextureMap,
    PixiFilters,
} from '../../../interfaces';
import { LuckyLennyAssetLoader } from '../../utils/LuckyLennyAssetLoader';
import { LAYER_ORDER } from '../../config';
import { createSolidBackground } from '../../utils/backgroundUtils';
import { calculateResponsiveScale } from '../../utils/responsiveScaleUtils';

// Extended interface to include container for infinite scrolling
interface IInfiniteBackgroundLayerGroup extends IBackgroundLayerGroup {
    container: Container;
    sprites: Sprite[];
    spriteWidth: number;
    parallaxX: number;
    parallaxY: number;
}

export class InfiniteBackground {
    public mainBackgroundLayerGroups: IInfiniteBackgroundLayerGroup[] = [];

    private backgroundContainer = new Container();

    private foregroundContainer = new Container();

    private screenWidth = 0;

    private screenHeight = 0;

    private baseAspectRatio = 1 / VIDEO_ASPECT_RATIO; // Mobile portrait (9:16)

    private backgroundTextures = {} as IBackgroundLayerTextureMap;

    private solidBackgroundSprite: Graphics | null = null;

    private isMobileView = false;

    private spriteTweens: Record<string, gsap.core.Tween> = {};

    constructor(
        private readonly stage: Container,
        private readonly viewport: PixiViewport,
    ) {
        this.backgroundContainer.label = 'backgroundContainer';
        this.backgroundContainer.zIndex = LAYER_ORDER.BACKGROUND;
        this.foregroundContainer.zIndex = LAYER_ORDER.FOREGROUND;

        this.stage.addChild(this.backgroundContainer, this.foregroundContainer);

        this.screenWidth = this.viewport.width;
        this.screenHeight = this.viewport.height;

        // Determine if this is mobile view
        this.isMobileView = this.screenWidth <= 414 || this.screenHeight <= 896; // change to pass value from main file

        // Create solid color background that covers the whole screen
        this.createSolidBackground();

        const backgroundTextures = LuckyLennyAssetLoader.getBackgroundTextures();

        if (backgroundTextures) {
            this.setupMainBackgroundLayers(backgroundTextures);

            // Keep containers positioned correctly relative to screen
            this.mainBackgroundLayerGroups.forEach((layerGroup) => {
                this.viewport.layoutManager.register(layerGroup.container, {
                    relativeTo: this.backgroundContainer,
                    horizontal: layerGroup.horizontal,
                    vertical: layerGroup.vertical,
                    offsetX: layerGroup.offset?.x || 0, // Uses offset from Background.ts config
                    offsetY: layerGroup.offset?.y || 0,
                    stickToScene: true,
                    baseScale: layerGroup.scale,
                    applyScale: true,
                });
            });
        }
    }

    public set filters(filters: PixiFilters) {
        this.backgroundContainer.filters = filters;
        this.foregroundContainer.filters = filters;
    }

    /**
     * Update screen dimensions
     */
    private updateScreenDimensions(width: number, height: number): void {
        this.screenWidth = width;
        this.screenHeight = height;
    }

    /**
     * Create a solid color background sprite that covers the whole screen
     */
    private createSolidBackground(): void {
        // Remove existing solid background if it exists
        if (this.solidBackgroundSprite) {
            this.backgroundContainer.removeChild(this.solidBackgroundSprite);
            this.solidBackgroundSprite.destroy();
        }

        // Create solid background using utility function
        this.solidBackgroundSprite = createSolidBackground({
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            backgroundColor: BASIC_BACKGROUND_SCALE,
            container: this.backgroundContainer,
        });
    }

    /**
     * Handle window resize
     */
    public handleResize(width: number, height: number): void {
        this.updateScreenDimensions(width, height);

        // Update solid background to cover new screen size
        this.createSolidBackground();

        if (this.mainBackgroundLayerGroups.length) {
            // Recalculate and apply new scaling
            this.updateBackgroundScaling();
        }
    }

    /**
     * Calculate appropriate scale based on screen aspect ratio and dimensions
     */
    private calculateResponsiveScale(baseScale: number): number {
        const result = calculateResponsiveScale({
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            baseScale,
            baseAspectRatio: this.baseAspectRatio,
        });

        return result.boundedScale;
    }

    /**
     * Update background scaling for all layers
     */
    private updateBackgroundScaling(): void {
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            // Skip if layer group is invalid
            if (!layerGroup.sprites.length) {
                Logger.warn('Background: Invalid layer group or empty sprites array');
                return;
            }

            this.updateSprites(layerGroup);
        });

        // Reset positions to prevent visual glitches
        this.resetParallaxPositions();
    }

    /**
     * Add or remove sprites as needed from layer group
     */
    private updateSprites(layerGroup: IInfiniteBackgroundLayerGroup): void {
        // Calculate responsive scale
        const responsiveScale = this.calculateResponsiveScale(layerGroup.scale);

        // Create initial sprites to cover screen width + buffer
        const backgroundTexture = this.backgroundTextures[layerGroup.spriteName];
        const spriteWidth = backgroundTexture.width * responsiveScale;

        // Update stored sprite width
        layerGroup.spriteWidth = spriteWidth;

        // Calculate number of sprites to cover screen width + buffer
        // Add extra sprites for mobile devices to ensure better coverage
        const baseSpritesNeeded = Math.ceil(this.screenWidth / spriteWidth) + 2;
        const isMobile = this.screenWidth <= 414 || this.screenHeight <= 896;
        const extraSpritesForMobile = isMobile ? 2 : 0; // More sprites for mobile to prevent gaps
        const spritesNeeded = baseSpritesNeeded + extraSpritesForMobile;

        // Remove excessive sprites if we have too many
        if (spritesNeeded < layerGroup.sprites.length) {
            const spritesToRemove = layerGroup.sprites.splice(spritesNeeded);
            spritesToRemove.forEach((sprite) => {
                layerGroup.container.removeChild(sprite);
                sprite.destroy();
            });
        }

        for (let i = 0; i < spritesNeeded; i++) {
            let sprite: Sprite;

            if (!layerGroup.sprites[i]) {
                sprite = new Sprite(backgroundTexture);
                sprite.anchor.copyFrom(layerGroup.anchor);

                layerGroup.sprites.push(sprite);
                layerGroup.container.addChild(sprite);
            } else {
                sprite = layerGroup.sprites[i];
            }

            // Update sprite scaling and positioning
            sprite.scale.set(responsiveScale);
            sprite.x = i * spriteWidth;
        }
    }

    public setupMainBackgroundLayers(backgroundTextures: IBackgroundLayerTextureMap): void {
        const configs = getMainBackgroundLayerConfigs(this.isMobileView);

        if (
            !backgroundTextures ||
            configs.some(
                (layerConfig: IBackgroundLayerGroup) => !backgroundTextures[layerConfig.spriteName],
            )
        ) {
            Logger.warn('Background: missing background textures');
            return;
        }

        // Store textures for resize handling
        this.backgroundTextures = backgroundTextures;

        // Clear existing background layers
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            this.getContainerByGroup(layerGroup).removeChild(layerGroup.container);
        });
        this.mainBackgroundLayerGroups = [];

        configs.forEach((config: IBackgroundLayerGroup) => {
            // Create container for this layer
            const container = new Container();
            container.zIndex = config.layerOrder;

            const layerGroup: IInfiniteBackgroundLayerGroup = {
                ...config,
                container,
                sprites: [],
                spriteWidth: 0,
                parallaxX: config.parallaxX || 0,
                parallaxY: config.parallaxY || 0,
            };

            this.updateSprites(layerGroup);
            this.mainBackgroundLayerGroups.push(layerGroup);
            this.getContainerByGroup(layerGroup).addChild(container);
        });
    }

    private getContainerByGroup(layerGroup: IInfiniteBackgroundLayerGroup) {
        return layerGroup.layerOrder === LAYER_ORDER.FOREGROUND
            ? this.foregroundContainer
            : this.backgroundContainer;
    }

    public reset(): void {
        // Reset all background layer positions
        this.resetParallaxPositions();
    }

    /**
     * Reset background layer positions
     */
    public resetParallaxPositions(): void {
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            const { sprites, spriteWidth } = layerGroup;

            // Reset sprite positions within container (containers stay positioned by layout manager)
            sprites.forEach((sprite, index) => {
                sprite.x = index * spriteWidth;
                sprite.y = 0;
            });
        });
    }

    /**
     * Apply parallax movement to background layers with infinite scrolling
     */
    public applyParallaxMovement(movementX: number, movementY: number): void {
        if (!this.mainBackgroundLayerGroups.length) {
            return;
        }

        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            const { sprites, parallaxX, parallaxY } = layerGroup;

            if (!sprites.length) {
                return;
            }

            // Apply horizontal parallax using configuration values
            const deltaX = movementX * parallaxX * 0.5;
            // Apply gentle vertical parallax using configuration values
            const deltaY = parallaxY * movementY * 0.2; // further soften

            sprites.forEach((sprite) => {
                sprite.x += deltaX;
                sprite.y += deltaY;
            });

            // Handle infinite scrolling for horizontal movement
            if (movementX !== 0) {
                this.handleInfiniteScrolling(layerGroup);
            }
        });
    }

    /**
     * Handle infinite scrolling logic for a background layer
     */
    private handleInfiniteScrolling(layerGroup: IInfiniteBackgroundLayerGroup): void {
        const { sprites, spriteWidth } = layerGroup;

        // Find the leftmost and rightmost sprites
        let leftmostSprite = sprites[0];
        let rightmostSprite = sprites[0];

        sprites.forEach((sprite) => {
            if (sprite.x < leftmostSprite.x) {
                leftmostSprite = sprite;
            }
            if (sprite.x > rightmostSprite.x) {
                rightmostSprite = sprite;
            }
        });

        // Check if leftmost sprite has moved too far left
        if (leftmostSprite.x < -spriteWidth) {
            // Move the leftmost sprite to the right of the rightmost sprite
            leftmostSprite.x = rightmostSprite.x + spriteWidth;
        }

        // Check if rightmost sprite has moved too far right (to prevent gaps on the left)
        if (rightmostSprite.x > this.screenWidth) {
            // Move the rightmost sprite to the left of the leftmost sprite
            rightmostSprite.x = leftmostSprite.x - spriteWidth;
        }
    }

    /**
     * Smoothly reset Y parallax positions back to baseline
     */
    public resetYParallax(duration: number): void {
        Logger.log(`[INFINITE BACKGROUND] Starting Y parallax reset with duration: ${duration}s`);

        this.mainBackgroundLayerGroups.forEach((layerGroup, index) => {
            const { sprites } = layerGroup;
            sprites.forEach((sprite, spriteIndex) => {
                this.spriteTweens[`${index}-${spriteIndex}`] = gsap.to(sprite, {
                    y: 0,
                    duration,
                    ease: 'power2.out',
                    onComplete: () => {
                        if (index === 0 && spriteIndex === 0) {
                            Logger.log('[INFINITE BACKGROUND] Y parallax reset complete');
                        }
                    },
                });
            });
        });
    }

    /**
     * Apply blur effect to background during collection
     */
    public applyBlurEffect(intensity = 2): void {
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            layerGroup.container.filters = [new BlurFilter({ strength: intensity })];
        });
    }

    /**
     * Remove blur effect from background
     */
    public removeBlurEffect(): void {
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            layerGroup.container.filters = null;
        });
    }

    /**
     * Override dispose to handle PixiViewport-specific layoutManager access
     */
    public dispose(): void {
        this.mainBackgroundLayerGroups.forEach((layerGroup) => {
            (this.viewport as PixiViewport).layoutManager.unregister(layerGroup.container);
            layerGroup.container.destroy({ children: true });
        });
        this.mainBackgroundLayerGroups = [];

        Object.values(this.spriteTweens).forEach((tween) => tween.kill());

        // Clean up solid background sprite
        if (this.solidBackgroundSprite) {
            this.solidBackgroundSprite.destroy();
            this.solidBackgroundSprite = null;
        }

        this.backgroundContainer.destroy({ children: true });
        this.backgroundContainer = null;
    }
}
