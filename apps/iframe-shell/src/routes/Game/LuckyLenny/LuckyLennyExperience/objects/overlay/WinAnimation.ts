import { AnimatedSprite, Container, Texture } from 'pixi.js';
import gsap from 'gsap';
import { Orientation } from '@bg-shared';
import { ReSpinText } from '../textAsSprite/ReSpinText';
import { AmountScaleThreshold, LuckyLennyEvents, LuckyLennyGameId, WinTypes } from '../../../enums';
import { ILuckyLennyEventManager } from '../../../interfaces';
import { FADE_DURATION, TURBO_MODE_SPEED_FACTOR } from '../../../constants';
import { CountUpAnimation } from '../CountUpAnimation';
import { AbstractContainer } from '../AbstractContainer';

interface IWinAnimationOptions {
    spriteTextures: Record<WinTypes, Texture[]>;
    eventManager: ILuckyLennyEventManager;
    orientation: Orientation;
    isTurboMode: boolean;
}

export class WinAnimation extends AbstractContainer {
    private readonly uiAdjustmentConfig = {
        [Orientation.PORTRAIT]: {
            y: -580,
            scale: 0.9,
        },
        [Orientation.LANDSCAPE]: {
            y: -170,
            scale: 0.6,
        },
    };

    private readonly countUpAnimation: CountUpAnimation;

    private readonly countUpScaleWrapper = new Container();

    private gameId: LuckyLennyGameId = LuckyLennyGameId.LennySlotGame;

    private tween: gsap.core.Tween | null = null;

    private winLabelSprite: AnimatedSprite | null = null;

    private readonly spriteTextures: Record<WinTypes, Texture[]>;

    private readonly eventManager: ILuckyLennyEventManager;

    private orientation: Orientation;

    private durationConfig = {
        main: 5,
        fadeOut: FADE_DURATION,
        countUp: 2,
    };

    constructor({ isTurboMode, spriteTextures, eventManager, orientation }: IWinAnimationOptions) {
        super();

        if (isTurboMode) {
            const turboModeFactor = TURBO_MODE_SPEED_FACTOR;

            this.durationConfig.main *= turboModeFactor;
            this.durationConfig.fadeOut *= turboModeFactor;
            this.durationConfig.countUp *= turboModeFactor;
        }

        this.view.on('destroyed', () => {
            this.dispose();
        });

        this.spriteTextures = spriteTextures;
        this.eventManager = eventManager;
        this.orientation = orientation;

        this.countUpAnimation = new CountUpAnimation({
            mainDuration: this.durationConfig.main,
            fadeOutDuration: this.durationConfig.fadeOut,
            countUpDuration: this.durationConfig.countUp,
        });
        this.countUpScaleWrapper.addChild(this.countUpAnimation.view);

        this.handleResize(orientation);

        this.addChild(this.countUpScaleWrapper);
    }

    public setGameId(gameId: LuckyLennyGameId) {
        this.gameId = gameId;
    }

    public show(
        {
            winAmount,
            winType,
            showPlusReSpin,
        }: { winAmount: number; winType?: WinTypes; showPlusReSpin: boolean },
        onComplete?: () => void,
    ): void {
        this.tween?.kill();

        const reSpinText = new ReSpinText();

        this.addChild(reSpinText.view);

        this.eventManager.trigger(LuckyLennyEvents.winAnimationStart);

        const onCompleteCommonHandler = () => {
            if (this.winLabelSprite) {
                this.winLabelSprite.visible = false;
                this.winLabelSprite.stop();
            }

            reSpinText.hide();
            onComplete?.();
        };

        if (this.gameId === LuckyLennyGameId.LennySlotGame) {
            const { scale, y } = this.uiAdjustmentConfig[this.orientation];

            this.scale.set(scale);
            this.position.y += y;
        }

        if (winAmount >= AmountScaleThreshold.twiceScaleDown) {
            this.countUpScaleWrapper.scale.set(this.countUpScaleWrapper.scale.x - 0.25);
        } else if (winAmount >= AmountScaleThreshold.scaleDown) {
            this.countUpScaleWrapper.scale.set(this.countUpScaleWrapper.scale.x - 0.2);
        }

        this.countUpAnimation.show(
            winAmount,
            () => {
                this.eventManager.trigger(LuckyLennyEvents.winAnimationFadeOut);

                this.tween?.kill();
                this.tween = gsap.to(this.view, {
                    alpha: 0,
                    duration: this.durationConfig.fadeOut,
                    ease: 'power2.out',
                    onComplete: onCompleteCommonHandler,
                });
            },
            () => {
                this.eventManager.trigger(LuckyLennyEvents.winAnimationEnd);
            },
        );

        if (showPlusReSpin) {
            reSpinText.position.y += 150;
            reSpinText.show();
        }

        if (!winType) {
            return;
        }

        const textures = this.spriteTextures[winType];

        if (!this.winLabelSprite) {
            this.winLabelSprite = new AnimatedSprite(textures);
            this.winLabelSprite.width *= 2;
            this.winLabelSprite.height *= 2;
            this.winLabelSprite.anchor.set(0.5, 0.75);
            this.winLabelSprite.animationSpeed = 0.5;
            this.winLabelSprite.loop = true;
            this.view.addChildAt(this.winLabelSprite, 0);
        } else {
            this.winLabelSprite.textures = textures;
        }

        this.winLabelSprite.visible = true;
        this.winLabelSprite.alpha = 1;
        this.winLabelSprite.play();
    }

    public handleResize(orientation: Orientation): void {
        this.orientation = orientation;
    }

    public destroy(): void {
        this.tween?.kill();
        this.tween = null;

        if (this.winLabelSprite) {
            this.winLabelSprite.destroy();
            this.winLabelSprite = null;
        }

        this.countUpAnimation.dispose();
    }
}
