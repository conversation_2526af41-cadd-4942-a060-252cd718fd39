import { Container } from 'pixi.js';
import gsap from 'gsap';
import { Orientation } from '@bg-shared';
import { AmountScaleThreshold } from '../../../enums';
import { TotalWinText } from '../textAsSprite/TotalWinText';
import { CountUpAnimation } from '../CountUpAnimation';
import { AbstractContainer } from '../AbstractContainer';

interface ITotalWinOptions {
    amount: number;
    fadeDuration: number;
    orientation: Orientation;
}

export class TotalWin extends AbstractContainer {
    private readonly uiAdjustmentConfig = {
        [Orientation.PORTRAIT]: {
            y: -620,
        },
        [Orientation.LANDSCAPE]: {
            y: -280,
        },
    };

    private readonly amount: number;

    private readonly totalWinText = new TotalWinText();

    private readonly totalWinAmount: CountUpAnimation;

    private readonly totalWinAmountScaleWrapper = new Container();

    private orientation: Orientation;

    private readonly fadeDuration: number;

    private tween: gsap.core.Tween | null = null;

    constructor({ amount, fadeDuration, orientation }: ITotalWinOptions) {
        super();

        this.amount = amount;
        this.orientation = orientation;
        this.fadeDuration = fadeDuration;
        this.totalWinAmount = new CountUpAnimation({
            mainDuration: 0,
            fadeOutDuration: this.fadeDuration,
            countUpDuration: 2,
        });

        this.create();
    }

    public show() {
        this.toAlpha(1);
    }

    public hide(callback?: ICallback) {
        this.toAlpha(0, callback);
    }

    public destroy() {
        this.tween?.kill();
        this.totalWinText.destroy();
        this.totalWinAmount.dispose();
        super.destroy();
    }

    private toAlpha(alpha: number, onComplete?: () => void) {
        this.tween?.kill();
        this.tween = gsap.to(this.view, {
            alpha,
            duration: this.fadeDuration,
            onComplete,
        });
    }

    private create() {
        const config = this.uiAdjustmentConfig[this.orientation];
        const GAP_Y = 20;

        this.alpha = 0;

        this.totalWinText.alpha = 1;
        this.totalWinText.visible = true;

        this.totalWinText.y += config.y;

        this.totalWinAmountScaleWrapper.addChild(this.totalWinAmount.view);
        this.totalWinAmountScaleWrapper.position.y =
            this.totalWinText.height + this.totalWinText.y + GAP_Y;

        if (this.orientation === Orientation.PORTRAIT) {
            if (this.amount >= AmountScaleThreshold.twiceScaleDown) {
                this.totalWinAmountScaleWrapper.scale.set(
                    this.totalWinAmountScaleWrapper.scale.x - 0.2,
                );
            } else if (this.amount >= AmountScaleThreshold.scaleDown) {
                this.totalWinAmountScaleWrapper.scale.set(
                    this.totalWinAmountScaleWrapper.scale.x - 0.1,
                );
            }
        }

        this.addChild(this.totalWinText.view, this.totalWinAmountScaleWrapper);

        this.totalWinAmount.updateAmount(this.amount);
    }
}
