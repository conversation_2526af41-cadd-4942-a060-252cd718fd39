import type { PointData, Sprite } from 'pixi.js';
import gsap from 'gsap';
import { round } from 'lodash-unified';
import { LuckyLennyAssetLoader } from '../utils/LuckyLennyAssetLoader';
import { AbstractContainer } from './AbstractContainer';
import { SpriteText } from './spriteText/SpriteText';

const enum PendingGsapTimer {
    Show = 1,
    Hide,
}

export class Multiplier extends AbstractContainer {
    public streakIndex = 0;

    public streakLength = 0;

    private readonly tween: Partial<Record<PendingGsapTimer, gsap.core.Tween>> = {};

    private multiplierValue: number;

    private spriteText: SpriteText;

    constructor(multiplierValue: number, anchor: PointData) {
        const spriteTextValue = new SpriteText({
            anchor,
            text: multiplierValue,
            textureMap: LuckyLennyAssetLoader.getMultiplierCharTexture(),
            customUpdateChar(char: string, charSprite: Sprite, maxHeight: number) {
                if (char === 'x') {
                    charSprite.y += maxHeight * 0.5;
                }
            },
        });

        super(spriteTextValue.view);

        this.spriteText = spriteTextValue;

        this.initial();
    }

    public set anchor(value: PointData) {
        this.spriteText.updateAnchor(value);
    }

    public set value(value: number) {
        this.multiplierValue = round(value);
        this.spriteText.text = `x${this.multiplierValue.toString()}`;
    }

    public get value() {
        return this.multiplierValue;
    }

    public show() {
        this.tween[PendingGsapTimer.Show]?.kill();
        this.initial();

        this.tween[PendingGsapTimer.Show] = gsap.to(this.scale, {
            x: 1,
            y: 1,
            duration: 0.5,
            ease: 'back.out(9)',
        });
    }

    public hide() {
        this.tween[PendingGsapTimer.Hide]?.kill();

        this.tween[PendingGsapTimer.Hide] = gsap.to(this, {
            alpha: 0,
            duration: 0.2,
            onComplete: () => this.destroy(),
        });
    }

    public destroy() {
        Object.values(this.tween).forEach((tween) => tween?.kill());
        super.destroy();
    }

    private initial() {
        this.alpha = 1;
        this.scale.set(0);
    }
}
