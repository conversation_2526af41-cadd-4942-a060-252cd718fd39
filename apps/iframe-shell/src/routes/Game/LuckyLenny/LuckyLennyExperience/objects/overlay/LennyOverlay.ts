import { Graphics } from 'pixi.js';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import gsap from 'gsap';
import { AbstractContainer } from '../AbstractContainer';
import { ILuckyLennyEventManager } from '../../../interfaces';
import { LuckyLennyEvents } from '../../../enums';
import { FADE_DURATION } from '../../../constants';

export class LennyOverlay extends AbstractContainer<Graphics> {
    private tween: gsap.core.Tween | null = null;

    protected eventUnsubscribers: (() => void)[] = [];

    constructor(
        private readonly viewport: PixiViewport,
        private readonly eventManager: ILuckyLennyEventManager,
    ) {
        super(new Graphics());

        this.drawRectangle(this.viewport.width, this.viewport.height);
        this.alpha = 0;
        this.visible = false;

        this.addEventListeners();
    }

    private addEventListeners(): void {
        this.eventUnsubscribers.push(
            this.eventManager.on(LuckyLennyEvents.winAnimationStart, () => {
                this.tween?.kill();

                this.visible = true;
                this.alpha = 0;

                this.tween = gsap.to(this, {
                    alpha: 0.5,
                    duration: FADE_DURATION,
                    ease: 'power2.out',
                });
            }),
            this.eventManager.on(LuckyLennyEvents.winAnimationFadeOut, () => {
                this.tween?.kill();
                this.tween = gsap.to(this, {
                    alpha: 0,
                    duration: FADE_DURATION,
                    ease: 'power2.out',
                    onComplete: () => {
                        this.visible = false;
                    },
                });
            }),
        );
    }

    public handleResize(width: number, height: number): void {
        this.drawRectangle(width, height);
    }

    public destroy(): void {
        this.viewport.layoutManager.unregister(this.view);

        this.tween?.kill();
        this.tween = null;

        super.destroy();
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];
    }

    private drawRectangle(width: number, height: number) {
        if (!this.view.parent) {
            return;
        }

        this.viewport.layoutManager.unregister(this.view);

        const actualWidth = width * (1 / this.view.parent.scale.x);
        const actualHeight = height * (1 / this.view.parent.scale.y);

        this.view
            .clear()
            .fill({ color: 0 })
            .rect(-actualWidth, -actualHeight, actualWidth * 1.5, actualHeight * 1.5)
            .fill();

        this.viewport.layoutManager.register(this.view, {
            horizontal: 'left',
            vertical: 'top',
            applyScale: false,
        });
    }
}
