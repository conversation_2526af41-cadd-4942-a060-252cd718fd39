import { ContainerChild } from 'pixi.js';
import gsap from 'gsap';
import { random } from 'lodash-unified';
import { AbstractContainer } from '../AbstractContainer';

type Range = [number, number];

interface IAbstractWheelOptions {
    segments: number;
    accelerateRange?: Range;
    holdPeakSpeed?: Range;
    decelerateRange?: Range;
    startSegment?: number;
    startEasing?: string;
    stopEasing?: string;
    speed?: number;
    angleStartMultiplier?: number;
    anglePeakMultiplier?: number;
    finalSpins?: number;
    clockwiseRotation?: boolean;
}

const enum WheelState {
    idle = 1,
    starting,
    peak,
    stopping,
}

const enum RotationDirection {
    clockwise = 1,
    counterclockwise = -1,
}

const fullCircleDeg = 360;

export abstract class AbstractWheel extends AbstractContainer {
    public readonly wheelSpriteLabel = 'wheel';

    public readonly wheelArrowSpriteLabel = 'arrow';

    protected readonly options: IAbstractWheelOptions;

    protected stoppingDuration = 0;

    protected spinDirection = RotationDirection.clockwise;

    protected wheelSprite: ContainerChild;

    private wheelArrowSprite: ContainerChild;

    private wheelTargetSegment = 0;

    private wheelState = WheelState.idle;

    private stopSpinListeners: ICallback[] = [];

    private startSpinListeners: ICallback[] = [];

    private wheelTween: gsap.core.Tween | null = null;

    private wheelArrowTween: gsap.core.Tween | null = null;

    constructor(options: IAbstractWheelOptions) {
        super();

        this.options = {
            accelerateRange: [1, 1.5],
            holdPeakSpeed: [0.5, 1],
            decelerateRange: [2, 3],
            startEasing: 'power2.in',
            stopEasing: 'elastic.out(0.8, 0.4)',
            speed: 1,
            angleStartMultiplier: fullCircleDeg,
            anglePeakMultiplier: 1080,
            finalSpins: 1,
            clockwiseRotation: true,
            startSegment: -1,
            ...options,
        };
    }

    public addStartSpinListener(cb: ICallback) {
        this.startSpinListeners.push(cb);
    }

    public addStopSpinListener(cb: ICallback) {
        this.stopSpinListeners.push(cb);
    }

    public get state() {
        return this.wheelState;
    }

    public get isIdle() {
        return this.state === WheelState.idle;
    }

    public create() {
        this.wheelSprite = this.view.getChildByLabel(this.wheelSpriteLabel);

        if (!this.wheelSprite) {
            throw new Error(`Main wheel container with label ${this.wheelSpriteLabel} required`);
        }

        this.wheelArrowSprite = this.view.getChildByLabel(this.wheelArrowSpriteLabel);

        if (this.options.startSegment > 0) {
            this.wheelSprite.angle = this.getTargetSegmentAngle(this.options.startSegment);
        } else {
            this.wheelSprite.angle = this.getTargetSegmentAngle(
                random(0, this.options.segments - 1),
            );
        }
    }

    public update() {}

    public spin(targetSegment?: number, clockwise?: boolean) {
        if (!this.isIdle) {
            return;
        }

        if (clockwise !== undefined) {
            this.spinDirection =
                clockwise ?? this.options.clockwiseRotation
                    ? RotationDirection.clockwise
                    : RotationDirection.counterclockwise;
        }

        this.arrowStartSpin();
        this.wheelState = WheelState.starting;

        this.wheelTargetSegment = targetSegment ?? Math.floor(random(true) * this.options.segments);

        this.startSpinListeners.forEach((callback) => callback());

        this.wheelTween?.kill();
        this.wheelTween = gsap.to(this.wheelSprite, {
            angle: `+=${
                this.options.angleStartMultiplier * this.options.speed * this.spinDirection
            }`,
            duration: random(
                this.options.accelerateRange[0],
                this.options.accelerateRange[1],
                true,
            ),
            ease: this.options.startEasing,
            onComplete: () => this.holdPeakSpeed(),
        });
    }

    public destroy() {
        this.stopSpinListeners.length = 0;
        this.wheelTween?.kill();
        this.wheelArrowTween?.kill();
        super.destroy();
    }

    protected beforeHoldPeakSpeed() {}

    protected afterHoldPeakSpeed() {}

    private arrowStartSpin() {
        if (this.wheelArrowSprite) {
            this.wheelArrowTween?.kill();
            this.wheelArrowTween = gsap.to(this.wheelArrowSprite, {
                angle: -30 * this.spinDirection,
                duration: 0.4,
                delay: 0.2,
            });
        }
    }

    private arrowStopping() {
        if (this.wheelArrowSprite) {
            this.wheelArrowTween?.kill();
            this.wheelArrowTween = gsap.to(this.wheelArrowSprite, {
                angle: 0,
                duration: this.stoppingDuration,
                ease: this.options.stopEasing,
            });
        }
    }

    private arrowStop() {
        if (this.wheelArrowSprite) {
            this.wheelArrowSprite.angle = 0;
        }
    }

    private idle() {
        this.wheelState = WheelState.idle;
    }

    private holdPeakSpeed() {
        this.beforeHoldPeakSpeed();
        this.wheelState = WheelState.peak;

        this.wheelTween?.kill();
        this.wheelTween = gsap.to(this.wheelSprite, {
            angle: `+=${
                this.options.anglePeakMultiplier * this.options.speed * this.spinDirection
            }`,
            duration: random(this.options.holdPeakSpeed[0], this.options.holdPeakSpeed[1], true),
            ease: 'none',
            onComplete: () => {
                this.afterHoldPeakSpeed();
                this.stopping();
            },
        });
    }

    private stopping() {
        this.stoppingDuration = random(
            this.options.decelerateRange[0],
            this.options.decelerateRange[1],
            true,
        );

        this.arrowStopping();
        this.wheelState = WheelState.stopping;

        const finalAngle =
            (this.options.finalSpins * fullCircleDeg +
                this.getTargetSegmentAngle(this.wheelTargetSegment)) *
            this.spinDirection;

        this.wheelSprite.angle %= fullCircleDeg;

        this.wheelTween?.kill();
        this.wheelTween = gsap.to(this.wheelSprite, {
            angle: finalAngle,
            duration: this.stoppingDuration,
            ease: this.options.stopEasing,
            onComplete: () => {
                this.arrowStop();
                this.idle();
                this.stopSpinListeners.forEach((cb) => cb());
            },
        });
    }

    protected getTargetSegmentAngle(targetSegment: number) {
        const segmentAngle = fullCircleDeg / this.options.segments;
        const target = targetSegment % this.options.segments;

        return (
            (this.spinDirection === RotationDirection.clockwise ? fullCircleDeg : 0) -
            target * segmentAngle * this.spinDirection
        );
    }
}
